# PLC分步执行技术方案

## 项目背景

F7_L12_Position_Detect 视觉检测系统当前支持三种触发模式：
- 机器人TCP指令触发（单个工具执行）
- 上位机TCP指令触发
- PLC监控触发（当前为全部工具执行）

## 问题分析

### 当前PLC模式的限制
- PLC触发时执行所有工具，无法分步执行
- 缺乏灵活性，不能根据生产需要执行特定工具
- 与机器人TCP模式的精确控制形成对比

### 技术需求
- 保持最小修改原则
- 向后兼容现有PLC程序
- 符合工程实践，便于PLC编程和调试
- 支持未来扩展

## 推荐方案：PLC寄存器多值映射

### 核心思路
利用PLC寄存器可以存储0、1、2等多个数值的特性，将不同数值映射到不同的执行动作。

### 映射关系设计

| PLC寄存器值 | 执行动作 | 对应工具 | 说明 |
|------------|---------|---------|------|
| 0 | 无动作 | - | 默认状态 |
| 1 | 执行所有工具 | Tools[0-n] | 保持原有逻辑 |
| 2 | 执行工具1 | Tools[0] | 新增功能 |
| 3 | 执行工具2 | Tools[1] | 新增功能 |
| 4 | 执行工具3 | Tools[2] | 新增功能 |
| ... | ... | ... | 可扩展 |

### 实现逻辑

```csharp
// 当前代码（仅支持全部执行）
if (Trig == 1)
{
    modbusTcp.Write((DataModel.Settingmodel.AddressStart).ToString(), (UInt16)0);
    // 执行所有工具...
}

// 修改后代码（支持分步执行）
if (Trig > 0)
{
    modbusTcp.Write((DataModel.Settingmodel.AddressStart).ToString(), (UInt16)0);
    
    if (Trig == 1)
    {
        // 保持原有逻辑：执行所有工具
        ExecuteAllTools();
    }
    else if (Trig >= 2 && Trig <= DataModel.Processmodel.Tools.Count + 1)
    {
        // 新逻辑：执行指定工具 (Trig-2对应工具索引)
        int toolIndex = Trig - 2;
        ExecuteSingleTool(toolIndex);
    }
}
```

## 方案优势

### 1. 最小修改原则
- 只需修改 `PLC_Process` 方法中的判断逻辑
- 不改变现有数据结构
- 不影响机器人TCP模式和上位机模式

### 2. 向后兼容
- 原有的"1=全部执行"逻辑完全保持
- 现有PLC程序无需任何修改即可继续工作
- 平滑升级，无风险

### 3. 工程实践友好
- PLC编程简单直观：写入2执行工具1，写入3执行工具2
- 无需复杂的位运算或多地址管理
- 调试方便，状态清晰可见
- 符合工控行业习惯

### 4. 扩展性强
- 可轻松支持更多工具（理论上支持65535个工具）
- 未来可扩展组合执行模式
- 为特殊需求预留空间

## 其他方案对比

### 方案一：多地址控制
```
优点：逻辑清晰，每个工具独立地址
缺点：
- 需要修改配置结构，增加多个地址字段
- PLC需要管理多个地址，增加复杂度
- 通讯开销增加
- 不符合最小修改原则
```

### 方案三：位控制模式
```
优点：一个地址控制多个工具组合
缺点：
- PLC编程复杂，需要位运算知识
- 调试困难，不直观
- 工控人员接受度低
- 扩展性受限于位数
```

## 实施建议

### 阶段一：代码修改
1. 修改 `PLC_Process` 方法的判断逻辑
2. 添加单个工具执行函数
3. 保持原有全部执行逻辑

### 阶段二：测试验证
1. 验证向后兼容性（寄存器值=1）
2. 测试单个工具执行（寄存器值=2,3,4...）
3. 验证错误处理和边界条件

### 阶段三：文档更新
1. 更新PLC接口文档
2. 提供PLC编程示例
3. 更新操作手册

## 风险评估

### 低风险
- 代码修改量小，影响范围可控
- 向后兼容，不影响现有系统
- 逻辑简单，不易出错

### 注意事项
- 需要验证工具索引边界检查
- 确保PLC复位逻辑正常工作
- 测试异常值处理（如寄存器值超出范围）

## 结论

**推荐采用PLC寄存器多值映射方案**，该方案完美平衡了功能需求、技术实现和工程实践的要求，是最符合项目实际情况的解决方案。

---
*文档创建时间：2025-01-31*  
*项目：F7_L12_Position_Detect*  
*版本：v1.0*
